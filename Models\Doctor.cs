using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HospitalReservationSystem.Models
{
    public class Doctor
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LicenseNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Specialization { get; set; } = string.Empty;

        [Required]
        public int DepartmentId { get; set; }

        [StringLength(20)]
        public string? OfficeNumber { get; set; }

        [StringLength(20)]
        public string? ContactNumber { get; set; }

        public decimal ConsultationFee { get; set; }

        public bool IsAvailable { get; set; } = true;

        public DateTime JoinDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("UserId")]
        public ApplicationUser User { get; set; } = null!;

        [ForeignKey("DepartmentId")]
        public Department Department { get; set; } = null!;

        public ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    }
}
