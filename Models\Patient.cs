using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HospitalReservationSystem.Models
{
    public class Patient
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string PatientNumber { get; set; } = string.Empty;

        [StringLength(20)]
        public string? EmergencyContact { get; set; }

        [StringLength(100)]
        public string? EmergencyContactName { get; set; }

        [StringLength(10)]
        public string? BloodGroup { get; set; }

        [StringLength(500)]
        public string? MedicalHistory { get; set; }

        [StringLength(500)]
        public string? Allergies { get; set; }

        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("UserId")]
        public ApplicationUser User { get; set; } = null!;

        public ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
        public ICollection<RoomAllocation> RoomAllocations { get; set; } = new List<RoomAllocation>();
    }
}
