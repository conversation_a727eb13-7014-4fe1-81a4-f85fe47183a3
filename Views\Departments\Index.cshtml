@model IEnumerable<HospitalReservationSystem.Models.Department>

@{
    ViewData["Title"] = "Department Management";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>@ViewData["Title"]</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Department
    </a>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5 class="mb-0">Hospital Departments</h5>
            </div>
            <div class="col-md-6">
                <input type="text" id="searchInput" class="form-control" placeholder="Search departments...">
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="departmentsTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Doctors Count</th>
                        <th>Status</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <strong>@item.Name</strong>
                            </td>
                            <td>@(item.Description ?? "No description")</td>
                            <td>
                                <span class="badge bg-info">@item.Doctors.Count</span>
                            </td>
                            <td>
                                @if (item.IsActive)
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Inactive</span>
                                }
                            </td>
                            <td>@item.CreatedDate.ToString("MMM dd, yyyy")</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form asp-action="Delete" asp-route-id="@item.Id" method="post" class="d-inline"
                                          onsubmit="return confirmDelete('Are you sure you want to delete this department?')">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize search functionality
        filterTable('searchInput', 'departmentsTable');
    </script>
}
