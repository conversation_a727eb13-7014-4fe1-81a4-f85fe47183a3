using Microsoft.AspNetCore.Identity;
using HospitalReservationSystem.Models;

namespace HospitalReservationSystem.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var context = serviceProvider.GetRequiredService<ApplicationDbContext>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Create roles
            string[] roleNames = { "Admin", "Doctor", "Patient" };
            foreach (var roleName in roleNames)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }

            // Create admin user
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);
            
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FirstName = "System",
                    LastName = "Administrator",
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }

            // Seed departments
            if (!context.Departments.Any())
            {
                var departments = new List<Department>
                {
                    new Department { Name = "Cardiology", Description = "Heart and cardiovascular system" },
                    new Department { Name = "Neurology", Description = "Brain and nervous system" },
                    new Department { Name = "Orthopedics", Description = "Bones, joints, and muscles" },
                    new Department { Name = "Pediatrics", Description = "Children's health" },
                    new Department { Name = "General Medicine", Description = "General medical care" },
                    new Department { Name = "Emergency", Description = "Emergency medical care" }
                };

                context.Departments.AddRange(departments);
                await context.SaveChangesAsync();
            }

            // Seed rooms
            if (!context.Rooms.Any())
            {
                var rooms = new List<Room>
                {
                    new Room { RoomNumber = "101", RoomType = RoomType.General, Capacity = 2, AvailableBeds = 2, DailyRate = 100 },
                    new Room { RoomNumber = "102", RoomType = RoomType.General, Capacity = 2, AvailableBeds = 2, DailyRate = 100 },
                    new Room { RoomNumber = "201", RoomType = RoomType.Private, Capacity = 1, AvailableBeds = 1, DailyRate = 200 },
                    new Room { RoomNumber = "202", RoomType = RoomType.Private, Capacity = 1, AvailableBeds = 1, DailyRate = 200 },
                    new Room { RoomNumber = "301", RoomType = RoomType.ICU, Capacity = 1, AvailableBeds = 1, DailyRate = 500 },
                    new Room { RoomNumber = "302", RoomType = RoomType.ICU, Capacity = 1, AvailableBeds = 1, DailyRate = 500 }
                };

                context.Rooms.AddRange(rooms);
                await context.SaveChangesAsync();
            }
        }
    }
}
