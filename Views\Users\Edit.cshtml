@model HospitalReservationSystem.ViewModels.EditUserViewModel

@{
    ViewData["Title"] = "Edit User";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="FirstName" class="form-control" placeholder="First Name" />
                                <label asp-for="FirstName"></label>
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="LastName" class="form-control" placeholder="Last Name" />
                                <label asp-for="LastName"></label>
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="Email" class="form-control" placeholder="Email" />
                        <label asp-for="Email"></label>
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="PhoneNumber" class="form-control" placeholder="Phone Number" />
                        <label asp-for="PhoneNumber"></label>
                        <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="SelectedRole" class="form-label"></label>
                        <select asp-for="SelectedRole" class="form-select">
                            <option value="">Select a role</option>
                            @foreach (var role in Model.AvailableRoles)
                            {
                                <option value="@role">@role</option>
                            }
                        </select>
                        <span asp-validation-for="SelectedRole" class="text-danger"></span>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Index" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>User Information</h6>
            </div>
            <div class="card-body">
                <p><strong>User ID:</strong> @Model.Id</p>
                <p class="text-muted">Note: Password cannot be changed here. Users must reset their password through the login page.</p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
