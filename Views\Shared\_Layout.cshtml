<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Hospital Reservation System</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HospitalReservationSystem.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-hospital"></i> Hospital Reservation System
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                        </li>
                        @if (User.IsInRole("Admin"))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cogs"></i> Admin
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Users" asp-action="Index">User Management</a></li>
                                    <li><a class="dropdown-item" asp-controller="Departments" asp-action="Index">Departments</a></li>
                                    <li><a class="dropdown-item" asp-controller="Doctors" asp-action="Index">Doctors</a></li>
                                    <li><a class="dropdown-item" asp-controller="Patients" asp-action="Index">Patients</a></li>
                                    <li><a class="dropdown-item" asp-controller="Rooms" asp-action="Index">Rooms</a></li>
                                </ul>
                            </li>
                        }
                        @if (User.IsInRole("Doctor") || User.IsInRole("Admin"))
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Appointments" asp-action="Index">
                                    <i class="fas fa-calendar-alt"></i> Appointments
                                </a>
                            </li>
                        }
                        @if (User.IsInRole("Patient"))
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Appointments" asp-action="MyAppointments">
                                    <i class="fas fa-calendar-check"></i> My Appointments
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Appointments" asp-action="Book">
                                    <i class="fas fa-plus"></i> Book Appointment
                                </a>
                            </li>
                        }
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2024 - Hospital Reservation System - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
