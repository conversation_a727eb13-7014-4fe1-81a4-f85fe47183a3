@model HospitalReservationSystem.Models.Department

@{
    ViewData["Title"] = "Edit Department";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <input asp-for="Id" type="hidden" />
                    <input asp-for="CreatedDate" type="hidden" />
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="form-floating mb-3">
                        <input asp-for="Name" class="form-control" placeholder="Department Name" />
                        <label asp-for="Name"></label>
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea asp-for="Description" class="form-control" placeholder="Description" style="height: 100px"></textarea>
                        <label asp-for="Description"></label>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="IsActive" class="form-check-input" />
                        <label asp-for="IsActive" class="form-check-label">
                            Active Department
                        </label>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Index" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Department</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>Department Details</h6>
            </div>
            <div class="card-body">
                <p><strong>Department ID:</strong> @Model.Id</p>
                <p><strong>Created:</strong> @Model.CreatedDate.ToString("MMM dd, yyyy")</p>
                <hr>
                <p class="text-muted">
                    Note: Deactivating a department will not affect existing doctors or appointments, 
                    but it will prevent new assignments.
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
