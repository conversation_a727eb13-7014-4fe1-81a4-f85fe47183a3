using System.ComponentModel.DataAnnotations;

namespace HospitalReservationSystem.Models
{
    public enum RoomType
    {
        General,
        Private,
        ICU,
        Emergency,
        Surgery,
        Maternity
    }

    public class Room
    {
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string RoomNumber { get; set; } = string.Empty;

        [Required]
        public RoomType RoomType { get; set; }

        public int Capacity { get; set; } = 1;

        public int AvailableBeds { get; set; } = 1;

        [StringLength(200)]
        public string? Description { get; set; }

        public decimal DailyRate { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public ICollection<RoomAllocation> RoomAllocations { get; set; } = new List<RoomAllocation>();
    }
}
