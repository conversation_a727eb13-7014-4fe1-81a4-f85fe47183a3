html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

/* Custom styles for Hospital Reservation System */
.navbar-brand {
  font-weight: bold;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.dashboard-card .card-body {
  padding: 2rem;
}

.dashboard-card h5 {
  font-size: 2rem;
  font-weight: bold;
}

.stats-card {
  border-left: 4px solid #007bff;
}

.stats-card.success {
  border-left-color: #28a745;
}

.stats-card.warning {
  border-left-color: #ffc107;
}

.stats-card.danger {
  border-left-color: #dc3545;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
}

.btn-group-sm > .btn, .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.form-floating > label {
  opacity: 0.65;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 1;
}

/* Status badges */
.badge.status-scheduled { background-color: #17a2b8; }
.badge.status-confirmed { background-color: #28a745; }
.badge.status-inprogress { background-color: #ffc107; color: #212529; }
.badge.status-completed { background-color: #6c757d; }
.badge.status-cancelled { background-color: #dc3545; }
.badge.status-noshow { background-color: #6f42c1; }

/* Room type badges */
.badge.room-general { background-color: #6c757d; }
.badge.room-private { background-color: #007bff; }
.badge.room-icu { background-color: #dc3545; }
.badge.room-emergency { background-color: #fd7e14; }
.badge.room-surgery { background-color: #6f42c1; }
.badge.room-maternity { background-color: #e83e8c; }
