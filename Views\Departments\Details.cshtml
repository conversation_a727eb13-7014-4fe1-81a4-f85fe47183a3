@model HospitalReservationSystem.Models.Department

@{
    ViewData["Title"] = "Department Details";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>@ViewData["Title"]</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit
        </a>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>@Model.Name</h4>
                @if (Model.IsActive)
                {
                    <span class="badge bg-success">Active</span>
                }
                else
                {
                    <span class="badge bg-secondary">Inactive</span>
                }
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">Description:</dt>
                    <dd class="col-sm-9">@(Model.Description ?? "No description provided")</dd>

                    <dt class="col-sm-3">Created Date:</dt>
                    <dd class="col-sm-9">@Model.CreatedDate.ToString("MMMM dd, yyyy")</dd>

                    <dt class="col-sm-3">Total Doctors:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-info">@Model.Doctors.Count</span>
                    </dd>
                </dl>
            </div>
        </div>

        @if (Model.Doctors.Any())
        {
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Doctors in this Department</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>License Number</th>
                                    <th>Specialization</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var doctor in Model.Doctors)
                                {
                                    <tr>
                                        <td>@doctor.User.FullName</td>
                                        <td>@doctor.LicenseNumber</td>
                                        <td>@doctor.Specialization</td>
                                        <td>@(doctor.ContactNumber ?? "N/A")</td>
                                        <td>
                                            @if (doctor.IsAvailable)
                                            {
                                                <span class="badge bg-success">Available</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">Unavailable</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="card mt-4">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                    <h5>No Doctors Assigned</h5>
                    <p class="text-muted">This department doesn't have any doctors assigned yet.</p>
                </div>
            </div>
        }
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-user-md"></i> Add Doctor
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> View Statistics
                    </a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6>Department Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">@Model.Doctors.Count</h4>
                        <small class="text-muted">Total Doctors</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@Model.Doctors.Count(d => d.IsAvailable)</h4>
                        <small class="text-muted">Available</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
