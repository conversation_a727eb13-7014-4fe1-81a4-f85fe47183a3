@model HospitalReservationSystem.Models.Department

@{
    ViewData["Title"] = "Create Department";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="form-floating mb-3">
                        <input asp-for="Name" class="form-control" placeholder="Department Name" />
                        <label asp-for="Name"></label>
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea asp-for="Description" class="form-control" placeholder="Description" style="height: 100px"></textarea>
                        <label asp-for="Description"></label>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="IsActive" class="form-check-input" checked />
                        <label asp-for="IsActive" class="form-check-label">
                            Active Department
                        </label>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Index" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Department</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>Department Information</h6>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    Create a new department to organize doctors and medical services. 
                    Departments help patients find the right medical care and doctors manage their specializations.
                </p>
                <hr>
                <h6>Examples:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-heart text-danger"></i> Cardiology</li>
                    <li><i class="fas fa-brain text-info"></i> Neurology</li>
                    <li><i class="fas fa-bone text-warning"></i> Orthopedics</li>
                    <li><i class="fas fa-child text-success"></i> Pediatrics</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
