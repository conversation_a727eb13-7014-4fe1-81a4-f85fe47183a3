@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome to Hospital Reservation System</h1>
    <p class="lead">Manage your healthcare appointments and medical records efficiently.</p>
</div>

@if (!User.Identity.IsAuthenticated)
{
    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">For Doctors</h5>
                    <p class="card-text">Manage your appointments, view patient records, and update medical information.</p>
                    <a href="#" class="btn btn-primary">Doctor <PERSON>gin</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-user-injured fa-3x text-success mb-3"></i>
                    <h5 class="card-title">For Patients</h5>
                    <p class="card-text">Book appointments, view your medical history, and manage your healthcare.</p>
                    <a href="#" class="btn btn-success">Patient Login</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-cogs fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">For Administrators</h5>
                    <p class="card-text">Manage hospital operations, users, departments, and system settings.</p>
                    <a href="#" class="btn btn-warning">Admin Login</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-12 text-center">
            <h3>Features</h3>
            <div class="row mt-4">
                <div class="col-md-3">
                    <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                    <h6>Appointment Booking</h6>
                    <p class="small">Easy online appointment scheduling</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-user-md fa-2x text-info mb-2"></i>
                    <h6>Doctor Management</h6>
                    <p class="small">Comprehensive doctor profiles and schedules</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-bed fa-2x text-info mb-2"></i>
                    <h6>Room Allocation</h6>
                    <p class="small">Efficient room and bed management</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                    <h6>Reports & Analytics</h6>
                    <p class="small">Detailed reports and insights</p>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h4>Welcome back, @User.Identity.Name!</h4>
                <p>You are logged in as: <strong>@(User.IsInRole("Admin") ? "Administrator" : User.IsInRole("Doctor") ? "Doctor" : "Patient")</strong></p>
            </div>
        </div>
    </div>
}
