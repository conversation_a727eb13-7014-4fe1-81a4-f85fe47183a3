@model IEnumerable<HospitalReservationSystem.ViewModels.UserViewModel>

@{
    ViewData["Title"] = "User Management";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>@ViewData["Title"]</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New User
    </a>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5 class="mb-0">System Users</h5>
            </div>
            <div class="col-md-6">
                <input type="text" id="searchInput" class="form-control" placeholder="Search users...">
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="usersTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Roles</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var user in Model)
                    {
                        <tr>
                            <td>@user.FullName</td>
                            <td>@user.Email</td>
                            <td>@(user.PhoneNumber ?? "N/A")</td>
                            <td>
                                @foreach (var role in user.Roles)
                                {
                                    <span class="badge bg-primary me-1">@role</span>
                                }
                                @if (!user.Roles.Any())
                                {
                                    <span class="badge bg-secondary">No Role</span>
                                }
                            </td>
                            <td>
                                @if (user.IsEmailConfirmed)
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-warning">Pending</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form asp-action="Delete" asp-route-id="@user.Id" method="post" class="d-inline"
                                          onsubmit="return confirmDelete('Are you sure you want to delete this user?')">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize search functionality
        filterTable('searchInput', 'usersTable');
    </script>
}
