using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using HospitalReservationSystem.Models;
using System.Diagnostics;

namespace HospitalReservationSystem.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                if (User.IsInRole("Admin"))
                {
                    return RedirectToAction("Dashboard", "Admin");
                }
                else if (User.<PERSON>In<PERSON><PERSON>("Doctor"))
                {
                    return RedirectToAction("Dashboard", "Doctor");
                }
                else if (User.IsInRole("Patient"))
                {
                    return RedirectToAction("Dashboard", "Patient");
                }
            }
            return View();
        }

        [Authorize(Roles = "Admin")]
        public IActionResult AdminDashboard()
        {
            return View();
        }

        [Authorize(Roles = "Doctor")]
        public IActionResult DoctorDashboard()
        {
            return View();
        }

        [Authorize(Roles = "Patient")]
        public IActionResult PatientDashboard()
        {
            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
