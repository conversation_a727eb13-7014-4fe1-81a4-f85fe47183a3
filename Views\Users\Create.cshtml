@model HospitalReservationSystem.ViewModels.CreateUserViewModel

@{
    ViewData["Title"] = "Create User";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="FirstName" class="form-control" placeholder="First Name" />
                                <label asp-for="FirstName"></label>
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="LastName" class="form-control" placeholder="Last Name" />
                                <label asp-for="LastName"></label>
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="Email" class="form-control" placeholder="Email" />
                        <label asp-for="Email"></label>
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="PhoneNumber" class="form-control" placeholder="Phone Number" />
                        <label asp-for="PhoneNumber"></label>
                        <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="SelectedRole" class="form-label"></label>
                        <select asp-for="SelectedRole" class="form-select">
                            <option value="">Select a role</option>
                            @foreach (var role in Model.AvailableRoles)
                            {
                                <option value="@role">@role</option>
                            }
                        </select>
                        <span asp-validation-for="SelectedRole" class="text-danger"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="Password" class="form-control" placeholder="Password" />
                                <label asp-for="Password"></label>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm Password" />
                                <label asp-for="ConfirmPassword"></label>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Index" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>User Roles</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><strong>Admin:</strong> Full system access</li>
                    <li><strong>Doctor:</strong> Manage appointments and patients</li>
                    <li><strong>Patient:</strong> Book appointments and view records</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
