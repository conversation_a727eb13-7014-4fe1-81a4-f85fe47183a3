using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HospitalReservationSystem.Models
{
    public enum AllocationStatus
    {
        Active,
        Discharged,
        Transferred
    }

    public class RoomAllocation
    {
        public int Id { get; set; }

        [Required]
        public int PatientId { get; set; }

        [Required]
        public int RoomId { get; set; }

        [Required]
        [DataType(DataType.DateTime)]
        public DateTime CheckInDate { get; set; } = DateTime.Now;

        [DataType(DataType.DateTime)]
        public DateTime? CheckOutDate { get; set; }

        [Required]
        public AllocationStatus Status { get; set; } = AllocationStatus.Active;

        [StringLength(500)]
        public string? Notes { get; set; }

        public decimal? TotalCharges { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("PatientId")]
        public Patient Patient { get; set; } = null!;

        [ForeignKey("RoomId")]
        public Room Room { get; set; } = null!;
    }
}
